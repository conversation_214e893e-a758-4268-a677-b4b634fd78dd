## Long-term Maintainability Features

### Documentation Standards
- **API Documentation**: Auto-generated with TypeScript types
- **Component Documentation**: JSDoc comments for all components
- **Database Documentation**: Schema documentation with relationships
- **Deployment Guide**: Step-by-step deployment instructions
- **Development Setup**: Local development environment setup

### Code Quality Assurance
```javascript
// .husky/pre-commit
#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

npm run lint:fix
npm run format
npm run type-check
npm run test -- --run
```

### Monitoring & Health Checks
```typescript
// src/app/api/health/route.ts
import { db } from '@/lib/db';
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Database health check
    await db.execute('SELECT 1');
    
    return NextResponse.json({
      status: 'healthy',
      database: 'connected',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    return NextResponse.json(
      { status: 'unhealthy', error: 'Database connection failed' },
      { status: 503 }
    );
  }
}
```

### Environment Management
```bash
# .env.example - Template for all environments
# Database
DATABASE_URL# IELTS Certification System - Complete Project Requirements

## Project Overview
Build a fast, lightweight IELTS certification management system using Next.js and PostgreSQL that handles candidate registration, result management, AI-powered feedback, and official certificate generation.

## Tech Stack Requirements (Latest Stable Versions)
- **Deployment**: Vercel (with serverless functions)
- **Frontend**: Next.js 15+ (App Router, React 19)
- **Database**: PostgreSQL (Neon) with Drizzle ORM
- **Authentication**: Auth.js v5 (NextAuth.js successor)
- **Styling**: Tailwind CSS v4 + shadcn/ui components
- **File Upload**: Vercel Blob + @vercel/blob
- **PDF Generation**: Puppeteer v23+ with chrome-aws-lambda
- **AI Integration**: Anthropic SDK v0.27+
- **Image Handling**: Next.js Image with Vercel optimization
- **Validation**: Zod v3.22+
- **State Management**: Zustand v4.4+ (for complex client state)
- **Database Client**: @vercel/postgres + Drizzle ORM
- **Type Safety**: TypeScript 5.3+
- **Testing**: Vitest + Playwright (for E2E)
- **Code Quality**: ESLint 9+ + Prettier + Husky

## Modern Database Schema (Drizzle ORM)

### Schema Definition (`src/lib/db/schema.ts`)
```typescript
import { pgTable, serial, varchar, text, timestamp, integer, decimal, boolean, pgEnum, date } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm/relations';

// Enums
export const userRoleEnum = pgEnum('user_role', ['admin', 'test_checker']);
export const documentTypeEnum = pgEnum('document_type', ['passport', 'national_id', 'driving_license', 'other']);

// Users table
export const users = pgTable('users', {
  id: serial('id').primaryKey(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  name: varchar('name', { length: 255 }).notNull(),
  role: userRoleEnum('role').notNull(),
  emailVerified: timestamp('email_verified'),
  image: varchar('image', { length: 500 }),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Candidates table
export const candidates = pgTable('candidates', {
  id: serial('id').primaryKey(),
  fullName: varchar('full_name', { length: 255 }).notNull(),
  documentType: documentTypeEnum('document_type').notNull(),
  documentNumber: varchar('document_number', { length: 100 }).notNull(),
  nationality: varchar('nationality', { length: 100 }),
  dateOfBirth: date('date_of_birth'),
  photoUrl: varchar('photo_url', { length: 500 }),
  registrationDate: timestamp('registration_date').defaultNow().notNull(),
  createdBy: integer('created_by').references(() => users.id),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Test results table
export const testResults = pgTable('test_results', {
  id: serial('id').primaryKey(),
  candidateId: integer('candidate_id').references(() => candidates.id).notNull(),
  testDate: date('test_date').notNull(),
  listeningScore: decimal('listening_score', { precision: 3, scale: 1 }),
  readingScore: decimal('reading_score', { precision: 3, scale: 1 }),
  writingTask1Score: decimal('writing_task1_score', { precision: 2, scale: 1 }),
  writingTask2Score: decimal('writing_task2_score', { precision: 2, scale: 1 }),
  speakingScore: decimal('speaking_score', { precision: 2, scale: 1 }),
  overallBandScore: decimal('overall_band_score', { precision: 2, scale: 1 }),
  certificateSerial: varchar('certificate_serial', { length: 20 }).unique(),
  aiFeedbackGenerated: boolean('ai_feedback_generated').default(false),
  enteredBy: integer('entered_by').references(() => users.id),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// AI feedback table  
export const aiFeedback = pgTable('ai_feedback', {
  id: serial('id').primaryKey(),
  testResultId: integer('test_result_id').references(() => testResults.id).notNull(),
  listeningFeedback: text('listening_feedback'),
  readingFeedback: text('reading_feedback'),
  writingFeedback: text('writing_feedback'),
  speakingFeedback: text('speaking_feedback'),
  overallFeedback: text('overall_feedback'),
  studyRecommendations: text('study_recommendations'),
  generatedAt: timestamp('generated_at').defaultNow().notNull(),
});

// Relations
export const usersRelations = relations(users, ({ many }) => ({
  candidatesCreated: many(candidates),
  resultsEntered: many(testResults),
}));

export const candidatesRelations = relations(candidates, ({ one, many }) => ({
  createdByUser: one(users, {
    fields: [candidates.createdBy],
    references: [users.id],
  }),
  testResults: many(testResults),
}));

export const testResultsRelations = relations(testResults, ({ one }) => ({
  candidate: one(candidates, {
    fields: [testResults.candidateId],
    references: [candidates.id],
  }),
  enteredByUser: one(users, {
    fields: [testResults.enteredBy],
    references: [users.id],
  }),
  aiFeedback: one(aiFeedback),
}));

export const aiFeedbackRelations = relations(aiFeedback, ({ one }) => ({
  testResult: one(testResults, {
    fields: [aiFeedback.testResultId],
    references: [testResults.id],
  }),
}));
```

### Database Configuration (`src/lib/db/index.ts`)
```typescript
import { drizzle } from 'drizzle-orm/vercel-postgres';
import { sql } from '@vercel/postgres';
import * as schema from './schema';

export const db = drizzle(sql, { schema });

export type Database = typeof db;
export * from './schema';
```

## Core Features & Pages

### 1. Authentication System
- **Login Page** (`/login`): NextAuth.js integration
- **Role-based Access Control**: Admin and Test Checker roles
- **Session Management**: Secure session handling

### 2. Admin Dashboard (`/admin`)
**Performance Requirements**: 
- Paginated candidate list (20 per page)
- Search by name/document number with debounced input
- Lazy loading for photos

**Features**:
- Register new candidates with photo upload
- View all candidates in optimized table
- Quick search functionality
- Bulk operations support

### 3. Test Checker Dashboard (`/checker`)
**Performance Requirements**:
- Efficient candidate lookup
- Batch result entry capabilities
- Real-time band score calculation

**Features**:
- Search candidates by document number
- Enter test results with automatic validation
- Generate certificate serial numbers
- Trigger AI feedback generation

### 4. Results Search Page (`/search`)
**Public Access - No Authentication Required**
**Performance Requirements**:
- Fast document number lookup with indexing
- Minimal data transfer
- CDN-optimized certificate delivery

**Features**:
- Search by document number
- Display basic result summary
- Link to detailed results page

### 5. Detailed Results Page (`/results/[id]`)
**Features**:
- Complete score breakdown
- AI-generated feedback and recommendations
- Certificate download functionality
- Performance comparison charts
- Study recommendations

### 6. Certificate Generation
**Requirements**:
- Identical to official IELTS certificate design
- Unique serial number generation
- PDF download functionality
- Verification system via serial number
- Candidate photo integration

## Vercel-Specific Optimizations

### Serverless Function Configuration
```javascript
// vercel.json
{
  "functions": {
    "src/app/api/feedback/generate/route.ts": {
      "maxDuration": 30
    },
    "src/app/api/certificate/[serial]/route.ts": {
      "maxDuration": 15
    }
  },
  "crons": [
    {
      "path": "/api/cleanup",
      "schedule": "0 2 * * *"
    }
  ]
}
```

### File Storage Strategy
- **Candidate Photos**: Vercel Blob Storage with automatic optimization
- **Generated Certificates**: Temporary generation with download links
- **File Size Limits**: 4.5MB per photo (Vercel limit compliance)

### Database Connection Management
```typescript
// lib/db.ts - Optimized for Vercel serverless
import { Pool } from 'pg';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  max: 1, // Vercel serverless optimization
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});
```

### Edge Runtime Optimization
```typescript
// AI feedback route - Edge runtime for faster cold starts
export const runtime = 'edge';
export const dynamic = 'force-dynamic';
```

## Performance Optimization Strategies
```sql
-- Essential Indexes
CREATE INDEX idx_candidates_document ON candidates(document_number);
CREATE INDEX idx_test_results_candidate ON test_results(candidate_id);
CREATE INDEX idx_test_results_serial ON test_results(certificate_serial);
CREATE INDEX idx_candidates_name ON candidates(full_name);
```

### Application-Level Optimizations
1. **Serverless-First Design**: All API routes optimized for Vercel Functions
2. **Edge Runtime**: Use Edge Runtime for lightweight operations
3. **Static Generation**: Pre-generate public pages where possible
4. **Image Optimization**: Vercel's automatic image optimization
5. **Database Connection Pooling**: Single connection per serverless function
6. **Caching Strategy**: Vercel KV for frequently accessed data
7. **Bundle Optimization**: Tree-shaking and code splitting
8. **Cold Start Minimization**: Lightweight dependencies and edge runtime

### Vercel-Specific File Handling
```typescript
// Photo upload with Vercel Blob
import { put } from '@vercel/blob';

export async function uploadCandidatePhoto(file: File, candidateId: string) {
  const blob = await put(`candidates/${candidateId}/photo.jpg`, file, {
    access: 'public',
    handleUploadUrl: '/api/upload',
  });
  return blob.url;
}
```

## AI Integration Specifications

### Claude 4.0 Sonnet Integration
**API Endpoint**: Use provided Claude API credentials
**Feedback Generation Prompt**:
```
Analyze this IELTS test result and provide detailed feedback:
- Listening: {score}/40
- Reading: {score}/40  
- Writing Task 1: {score}/9
- Writing Task 2: {score}/9
- Speaking: {score}/9
- Overall Band: {calculated_band}

Provide:
1. Individual module analysis with strengths and weaknesses
2. Specific study recommendations for each weak area
3. Overall improvement strategy
4. Realistic timeline for score improvement
5. Recommended study materials and techniques
```

### Feedback Categories
- **Module-Specific**: Targeted advice for each skill
- **Overall Strategy**: Holistic improvement approach
- **Study Resources**: Specific book/online recommendations
- **Practice Schedule**: Customized study timeline
- **Weakness Analysis**: Detailed breakdown of problem areas

## Security Requirements

### Data Protection
- Secure file upload with type validation
- Photo storage with access controls
- Database connection encryption (SSL)
- Input sanitization and validation
- Role-based access control

### Certificate Security
- Unique serial number generation (format: IELTS-YYYY-NNNNNN)
- Certificate verification endpoint
- Tamper-proof PDF generation
- Digital signature implementation

## Project Structure (Enterprise-Ready)
```
/
├── .github/
│   └── workflows/
│       ├── ci.yml                    # Continuous Integration
│       └── deploy.yml                # Deployment workflow
├── docs/
│   ├── api/                          # API documentation
│   ├── deployment.md                 # Deployment guide
│   └── development.md                # Development setup
├── scripts/
│   ├── seed.ts                       # Database seeding
│   ├── migrate.ts                    # Migration runner
│   └── backup.ts                     # Database backup
├── src/
│   ├── app/
│   │   ├── (auth)/
│   │   │   └── login/
│   │   │       └── page.tsx
│   │   ├── (dashboard)/
│   │   │   ├── admin/
│   │   │   │   ├── page.tsx          # Admin dashboard
│   │   │   │   ├── candidates/
│   │   │   │   │   ├── page.tsx      # Candidates list
│   │   │   │   │   ├── new/
│   │   │   │   │   │   └── page.tsx  # Register candidate
│   │   │   │   │   └── [id]/
│   │   │   │   │       └── page.tsx  # Candidate detail
│   │   │   │   └── settings/
│   │   │   │       └── page.tsx      # System settings
│   │   │   └── checker/
│   │   │       ├── page.tsx          # Test checker dashboard
│   │   │       ├── results/
│   │   │       │   ├── page.tsx      # Results entry
│   │   │       │   └── [id]/
│   │   │       │       └── page.tsx  # Edit results
│   │   │       └── search/
│   │   │           └── page.tsx      # Search candidates
│   │   ├── (public)/
│   │   │   ├── search/
│   │   │   │   └── page.tsx          # Public search
│   │   │   ├── results/
│   │   │   │   └── [id]/
│   │   │   │       └── page.tsx      # Detailed results
│   │   │   └── verify/
│   │   │       └── [serial]/
│   │   │           └── page.tsx      # Certificate verification
│   │   ├── api/
│   │   │   ├── auth/
│   │   │   │   └── [...nextauth]/
│   │   │   │       └── route.ts      # Auth.js configuration
│   │   │   ├── candidates/
│   │   │   │   ├── route.ts          # CRUD operations
│   │   │   │   ├── [id]/
│   │   │   │   │   └── route.ts      # Individual candidate
│   │   │   │   └── search/
│   │   │   │       └── route.ts      # Search endpoint
│   │   │   ├── results/
│   │   │   │   ├── route.ts          # Results CRUD
│   │   │   │   └── [id]/
│   │   │   │       ├── route.ts      # Individual result
│   │   │   │       └── feedback/
│   │   │   │           └── route.ts  # AI feedback
│   │   │   ├── certificates/
│   │   │   │   ├── [serial]/
│   │   │   │   │   └── route.ts      # Download/verify
│   │   │   │   └── generate/
│   │   │   │       └── route.ts      # Generate certificate
│   │   │   ├── upload/
│   │   │   │   └── route.ts          # File upload handler
│   │   │   └── health/
│   │   │       └── route.ts          # Health check
│   │   ├── globals.css               # Global styles
│   │   ├── layout.tsx                # Root layout
│   │   ├── loading.tsx               # Global loading UI
│   │   ├── error.tsx                 # Global error boundary
│   │   └── not-found.tsx             # 404 page
│   ├── components/
│   │   ├── ui/                       # shadcn/ui components
│   │   │   ├── button.tsx
│   │   │   ├── input.tsx
│   │   │   ├── dialog.tsx
│   │   │   ├── table.tsx
│   │   │   └── toast.tsx
│   │   ├── forms/                    # Form components
│   │   │   ├── candidate-form.tsx
│   │   │   ├── results-form.tsx
│   │   │   └── search-form.tsx
│   │   ├── layouts/                  # Layout components
│   │   │   ├── dashboard-layout.tsx
│   │   │   ├── auth-layout.tsx
│   │   │   └── public-layout.tsx
│   │   ├── charts/                   # Data visualization
│   │   │   ├── results-chart.tsx
│   │   │   └── performance-chart.tsx
│   │   └── features/                 # Feature-specific components
│   │       ├── candidates/
│   │       ├── results/
│   │       └── certificates/
│   ├── lib/
│   │   ├── auth/
│   │   │   ├── config.ts             # Auth.js configuration
│   │   │   └── middleware.ts         # Auth middleware
│   │   ├── db/
│   │   │   ├── index.ts              # Database client
│   │   │   ├── schema.ts             # Drizzle schema
│   │   │   └── migrations/           # Database migrations
│   │   ├── services/                 # Business logic
│   │   │   ├── candidates.ts
│   │   │   ├── results.ts
│   │   │   ├── certificates.ts
│   │   │   └── ai-feedback.ts
│   │   ├── utils/
│   │   │   ├── validations.ts        # Zod schemas
│   │   │   ├── calculations.ts       # IELTS band calculations
│   │   │   ├── file-upload.ts        # File handling
│   │   │   └── pdf-generator.ts      # PDF generation
│   │   ├── hooks/                    # Custom React hooks
│   │   │   ├── use-candidates.ts
│   │   │   ├── use-results.ts
│   │   │   └── use-feedback.ts
│   │   └── constants/
│   │       ├── routes.ts             # Application routes
│   │       ├── band-scores.ts        # IELTS scoring tables
│   │       └── validations.ts        # Validation constants
│   ├── stores/                       # Zustand stores
│   │   ├── candidates.ts
│   │   ├── results.ts
│   │   └── ui.ts
│   └── types/
│       ├── auth.ts                   # Authentication types
│       ├── database.ts               # Database types
│       ├── api.ts                    # API response types
│       └── index.ts                  # Common types
├── tests/
│   ├── __mocks__/                    # Test mocks
│   ├── e2e/                          # Playwright tests
│   ├── unit/                         # Unit tests
│   └── setup.ts                      # Test setup
├── .env.example                      # Environment variables template
├── .env.local                        # Local environment (gitignored)
├── .gitignore
├── .eslintrc.json                    # ESLint configuration
├── .prettierrc                       # Prettier configuration
├── drizzle.config.ts                 # Drizzle configuration
├── next.config.js                    # Next.js configuration
├── tailwind.config.ts                # Tailwind configuration
├── tsconfig.json                     # TypeScript configuration
├── vercel.json                       # Vercel configuration
├── vitest.config.ts                  # Vitest configuration
├── playwright.config.ts              # Playwright configuration
├── package.json
└── README.md
```

## API Endpoints

### Authentication
- `POST /api/auth/signin` - User login
- `POST /api/auth/signout` - User logout

### Candidates
- `GET /api/candidates` - List candidates (paginated)
- `POST /api/candidates` - Register new candidate
- `GET /api/candidates/search` - Search candidates

### Results
- `POST /api/results` - Enter test results
- `GET /api/results/[id]` - Get detailed results
- `GET /api/search` - Public result search

### AI Feedback
- `POST /api/feedback/generate` - Generate AI feedback
- `GET /api/feedback/[resultId]` - Get generated feedback

### Certificates
- `GET /api/certificate/[serial]` - Download certificate PDF
- `GET /api/certificate/verify/[serial]` - Verify certificate

## Development Priorities

### Phase 1: Core Infrastructure
1. Database setup and migrations
2. Authentication system
3. Basic CRUD operations
4. Admin dashboard with candidate registration

### Phase 2: Result Management
1. Test checker interface
2. Result entry system
3. Automatic band score calculation
4. Basic search functionality

### Phase 3: AI & Certificates
1. Claude API integration
2. AI feedback generation
3. Certificate design and PDF generation
4. Serial number system

### Phase 4: Optimization & Polish
1. Performance optimizations
2. Advanced search features
3. Detailed analytics
4. UI/UX improvements

## Performance Benchmarks
- **Page Load Time**: < 2 seconds
- **Database Queries**: < 100ms average
- **Search Results**: < 500ms
- **PDF Generation**: < 3 seconds
- **AI Feedback**: < 10 seconds

## Environment Variables Required
```env
# Core Application
NEXTAUTH_SECRET=your-secret
NEXTAUTH_URL=https://your-vercel-app.vercel.app
DATABASE_URL=postgresql://ielts-certification-system_owner:<EMAIL>/ielts-certification-system?sslmode=require

# AI Integration
CLAUDE_API_KEY=your-claude-api-key

# Vercel Services
BLOB_READ_WRITE_TOKEN=your-vercel-blob-token
KV_REST_API_URL=your-kv-url
KV_REST_API_TOKEN=your-kv-token

# Public URLs
NEXT_PUBLIC_APP_URL=https://your-vercel-app.vercel.app
NEXT_PUBLIC_BLOB_URL=your-blob-storage-url
```

## Configuration Files

### Next.js Configuration (`next.config.js`)
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    ppr: true, // Partial Pre-rendering
    dynamicIO: true, // Dynamic IO optimization
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '*.vercel-storage.com',
      },
    ],
  },
  eslint: {
    dirs: ['src'],
  },
  typescript: {
    ignoreBuildErrors: false,
  },
};

module.exports = nextConfig;
```

### Drizzle Configuration (`drizzle.config.ts`)
```typescript
import type { Config } from 'drizzle-kit';

export default {
  schema: './src/lib/db/schema.ts',
  out: './src/lib/db/migrations',
  driver: 'pg',
  dbCredentials: {
    connectionString: process.env.DATABASE_URL!,
  },
  verbose: true,
  strict: true,
} satisfies Config;
```

### TypeScript Configuration (`tsconfig.json`)
```json
{
  "compilerOptions": {
    "target": "ES2017",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"],
      "@/lib/*": ["./src/lib/*"],
      "@/types/*": ["./src/types/*"],
      "@/stores/*": ["./src/stores/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
```

### ESLint Configuration (`.eslintrc.json`)
```json
{
  "extends": [
    "next/core-web-vitals",
    "@typescript-eslint/recommended",
    "prettier"
  ],
  "parser": "@typescript-eslint/parser",
  "plugins": ["@typescript-eslint"],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/no-explicit-any": "warn",
    "@typescript-eslint/consistent-type-imports": "error",
    "prefer-const": "error",
    "no-var": "error"
  },
  "ignorePatterns": ["node_modules/", ".next/", "out/"]
}
```

### Prettier Configuration (`.prettierrc`)
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "tabWidth": 2,
  "printWidth": 80,
  "plugins": ["prettier-plugin-tailwindcss"]
}
```

## Project Setup & Dependencies

### Package.json Template
```json
{
  "name": "ielts-certification-system",
  "version": "1.0.0",
  "private": true,
  "scripts": {
    "dev": "next dev --turbo",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "lint:fix": "next lint --fix",
    "type-check": "tsc --noEmit",
    "format": "prettier --write .",
    "format:check": "prettier --check .",
    "test": "vitest",
    "test:e2e": "playwright test",
    "db:generate": "drizzle-kit generate",
    "db:migrate": "drizzle-kit migrate",
    "db:studio": "drizzle-kit studio",
    "db:seed": "tsx scripts/seed.ts",
    "prepare": "husky install",
    "vercel-build": "npm run db:migrate && npm run build"
  },
  "dependencies": {
    "next": "^15.0.0",
    "react": "^19.0.0",
    "react-dom": "^19.0.0",
    "@auth/core": "^0.35.0",
    "@auth/drizzle-adapter": "^1.5.0",
    "next-auth": "5.0.0-beta.22",
    "@vercel/postgres": "^0.10.0",
    "@vercel/blob": "^0.24.0",
    "drizzle-orm": "^0.33.0",
    "drizzle-kit": "^0.25.0",
    "@anthropic-ai/sdk": "^0.27.0",
    "zod": "^3.23.0",
    "zustand": "^4.4.0",
    "tailwindcss": "^4.0.0",
    "@tailwindcss/forms": "^0.5.0",
    "@radix-ui/react-slot": "^1.1.0",
    "@radix-ui/react-dialog": "^1.1.0",
    "@radix-ui/react-dropdown-menu": "^2.1.0",
    "@radix-ui/react-toast": "^1.2.0",
    "class-variance-authority": "^0.7.0",
    "clsx": "^2.1.0",
    "tailwind-merge": "^2.5.0",
    "lucide-react": "^0.445.0",
    "puppeteer": "^23.5.0",
    "chrome-aws-lambda": "^10.1.0",
    "date-fns": "^4.1.0",
    "sharp": "^0.33.0",
    "nanoid": "^5.0.0"
  },
  "devDependencies": {
    "@types/node": "^22.7.0",
    "@types/react": "^18.3.0",
    "@types/react-dom": "^18.3.0",
    "typescript": "^5.6.0",
    "eslint": "^9.11.0",
    "eslint-config-next": "^15.0.0",
    "@typescript-eslint/eslint-plugin": "^8.8.0",
    "@typescript-eslint/parser": "^8.8.0",
    "prettier": "^3.3.0",
    "prettier-plugin-tailwindcss": "^0.6.0",
    "husky": "^9.1.0",
    "lint-staged": "^15.2.0",
    "vitest": "^2.1.0",
    "@vitejs/plugin-react": "^4.3.0",
    "playwright": "^1.47.0",
    "@playwright/test": "^1.47.0",
    "tsx": "^4.19.0"
  },
  "lint-staged": {
    "*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"],
    "*.{json,md,css}": ["prettier --write"]
  }
}
```

### Vercel Build Settings
- **Framework Preset**: Next.js
- **Build Command**: `npm run vercel-build`
- **Output Directory**: `.next`
- **Install Command**: `npm ci`
- **Development Command**: `npm run dev`

## Performance Benchmarks (Vercel-Optimized)
- **Cold Start Time**: < 1 second
- **Page Load Time**: < 1.5 seconds (with Vercel Edge Network)
- **Database Queries**: < 200ms (including connection time)
- **Image Optimization**: Automatic WebP/AVIF conversion
- **PDF Generation**: < 5 seconds (serverless function timeout)
- **AI Feedback**: < 15 seconds (with extended timeout)
- **Global CDN**: Sub-100ms static asset delivery